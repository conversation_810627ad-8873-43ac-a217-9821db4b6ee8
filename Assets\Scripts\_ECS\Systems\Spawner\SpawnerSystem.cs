using Unity.Entities;
using Unity.Transforms;
using Unity.Mathematics;
using static Unity.Entities.SystemAPI;
using Unity.Collections;
using PlayerFAP.Components;
using Rukhanka.Test;
using Rukhanka.Test.Pooling;

namespace ProjectDawn.Navigation.Sample.Crowd
{
    [RequireMatchingQueriesForUpdate]
    [UpdateInGroup(typeof(FixedStepSimulationSystemGroup))]
    [UpdateBefore(typeof(AgentSystemGroup))]
    public partial struct SpawnerSystem : ISystem
    {
        ComponentLookup<CrowdGroup> m_GroupLookup;
        EndInitializationEntityCommandBufferSystem.Singleton m_CommandBuffer;
        EntityQuery m_SpawnedEntitiesQuery;
        Entity m_PoolManagerEntity;

        public void OnCreate(ref SystemState state)
        {
            state.RequireForUpdate<EndInitializationEntityCommandBufferSystem.Singleton>();

            m_SpawnedEntitiesQuery = state.EntityManager.CreateEntityQuery(
                ComponentType.ReadOnly<SpawnerLink>(),
                ComponentType.Exclude<DeadTag>()
            );

            var poolManagerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<PoolManagerTag>());
            if (poolManagerQuery.CalculateEntityCount() > 0)
            {
                m_PoolManagerEntity = poolManagerQuery.GetSingletonEntity();
            }
            else
            {
                m_PoolManagerEntity = Entity.Null;
#if UNITY_EDITOR
                DebugLogManager.Instance.Log("SpawnerSystem.OnCreate: No PoolManager found! Spawning will not work.",
                    DebugLogSettings.LogType.EnemySpawner);
#endif
            }
        }

        public void OnUpdate(ref SystemState state)
        {
            if (SystemAPI.HasSingleton<UsePoolingSystemTag>())
            {
                // Refresh pool manager entity in case it was created after this system
                if (m_PoolManagerEntity == Entity.Null || !state.EntityManager.Exists(m_PoolManagerEntity))
                {
                    var poolManagerQueryUpdate =
                        state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<PoolManagerTag>());
                    if (poolManagerQueryUpdate.CalculateEntityCount() > 0)
                    {
                        m_PoolManagerEntity = poolManagerQueryUpdate.GetSingletonEntity();
                    }
                }

                if (m_PoolManagerEntity == Entity.Null)
                {
#if UNITY_EDITOR
                    DebugLogManager.Instance.Log("SpawnerSystem.OnUpdate: No PoolManager available, skipping spawn",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                    return;
                }

                m_CommandBuffer = GetSingleton<EndInitializationEntityCommandBufferSystem.Singleton>();

#if UNITY_EDITOR
                DebugLogManager.Instance.Log("SpawnerSystem.OnUpdate: Running spawner system",
                    DebugLogSettings.LogType.EnemySpawner);
#endif

                var spawnerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<Spawner>());
                int spawnerCount = spawnerQuery.CalculateEntityCount();
#if UNITY_EDITOR
                DebugLogManager.Instance.Log($"SpawnerSystem.OnUpdate: Found {spawnerCount} spawners",
                    DebugLogSettings.LogType.EnemySpawner);
#endif

                // Count entities with SpawnerLink (these are actively spawned entities)
                var actualSpawnedCounts = new NativeHashMap<Entity, int>(spawnerCount, Allocator.Temp);

                foreach (var (spawnerLink, entity) in SystemAPI.Query<RefRO<SpawnerLink>>()
                             .WithEntityAccess()
                             .WithNone<DeadTag>()
                             .WithAll<InUse>())
                {
                    if (actualSpawnedCounts.TryGetValue(spawnerLink.ValueRO.SpawnerEntity, out int currentActualCount))
                    {
                        actualSpawnedCounts[spawnerLink.ValueRO.SpawnerEntity] = currentActualCount + 1;
                    }
                    else
                    {
                        actualSpawnedCounts.TryAdd(spawnerLink.ValueRO.SpawnerEntity, 1);
                    }
                }

                foreach (var (spawnerRef, spawnerEntity) in SystemAPI.Query<RefRW<Spawner>>().WithEntityAccess())
                {
                    if (spawnerRef.ValueRO.ContinuousSpawning)
                    {
                        actualSpawnedCounts.TryGetValue(spawnerEntity, out int actualCountForThisSpawner);

                        if (spawnerRef.ValueRO.Count != actualCountForThisSpawner)
                        {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                            DebugLogManager.Instance.Log(
                                $"Updating spawner {spawnerEntity} count from {spawnerRef.ValueRO.Count} to {actualCountForThisSpawner}",
                                DebugLogSettings.LogType.EnemySpawner);
#endif
                            spawnerRef.ValueRW.Count = actualCountForThisSpawner;
                        }
                    }
                }

                actualSpawnedCounts.Dispose();

                var elapsedTime = (float)state.WorldUnmanaged.Time.ElapsedTime;
                var ecb = m_CommandBuffer.CreateCommandBuffer(state.WorldUnmanaged);

                // Process entities that need to be returned to pool
                //ProcessReturnToPool(ref state, ecb);

                // Reset navigation for newly spawned entities from pool - MUST be after pooling system runs
                ResetNavigationForPooledEntities(ref state, ecb);

                foreach (var (spawner, transform, entity) in
                         SystemAPI.Query<RefRW<Spawner>, RefRO<LocalToWorld>>().WithEntityAccess())
                {
                    ProcessSpawner(ref spawner.ValueRW, transform.ValueRO.Position, entity, ecb,
                        state.WorldUnmanaged.Time.DeltaTime, elapsedTime, m_PoolManagerEntity);
                }
            }
            else
            {
                // Debug: Log that the SpawnerSystem is running
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                DebugLogManager.Instance.Log("SpawnerSystem.OnUpdate: Running spawner system",
                    DebugLogSettings.LogType.EnemySpawner);
#endif

                // Ensure command buffer for normal (non-pooled) spawning path
                m_CommandBuffer = GetSingleton<EndInitializationEntityCommandBufferSystem.Singleton>();

                // Debug: Count spawners
                var spawnerQuery = state.EntityManager.CreateEntityQuery(ComponentType.ReadOnly<NormalSpawner>());
                int spawnerCount = spawnerQuery.CalculateEntityCount();
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                DebugLogManager.Instance.Log($"SpawnerSystem.OnUpdate: Found {spawnerCount} spawners",
                    DebugLogSettings.LogType.EnemySpawner);
#endif

                // Optimize continuous spawner count update
                var actualSpawnedCounts = new NativeHashMap<Entity, int>(spawnerCount, Allocator.Temp);

                // 1. Aggregate actual counts for all spawners
                // Assuming entities with SpawnerLink also have LocalTransform as per original logic context
                foreach (var (spawnerLink, entity) in SystemAPI.Query<RefRO<SpawnerLink>>().WithEntityAccess()
                             .WithNone<DeadTag>())
                {
                    if (actualSpawnedCounts.TryGetValue(spawnerLink.ValueRO.SpawnerEntity, out int currentActualCount))
                    {
                        actualSpawnedCounts[spawnerLink.ValueRO.SpawnerEntity] = currentActualCount + 1;
                    }
                    else
                    {
                        actualSpawnedCounts.TryAdd(spawnerLink.ValueRO.SpawnerEntity, 1);
                    }
                }

                // 2. Update Spawner components if continuous spawning is enabled and counts differ
                foreach (var (spawnerRef, spawnerEntity) in SystemAPI.Query<RefRW<NormalSpawner>>().WithEntityAccess())
                {
                    if (spawnerRef.ValueRO.ContinuousSpawning)
                    {
                        actualSpawnedCounts.TryGetValue(spawnerEntity,
                            out int actualCountForThisSpawner); // Default is 0 if not found

                        if (spawnerRef.ValueRO.Count != actualCountForThisSpawner)
                        {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                            DebugLogManager.Instance.Log(
                                $"Updating spawner {spawnerEntity} count from {spawnerRef.ValueRO.Count} to {actualCountForThisSpawner}",
                                DebugLogSettings.LogType.EnemySpawner);
#endif
                            spawnerRef.ValueRW.Count = actualCountForThisSpawner;
                        }
                    }
                }

                actualSpawnedCounts.Dispose();

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                // Debug: Log spawner details
                var updatedSpawnerComponentsForLogging =
                    spawnerQuery.ToComponentDataArray<NormalSpawner>(Unity.Collections.Allocator.Temp);
                for (int i = 0; i < updatedSpawnerComponentsForLogging.Length; i++)
                {
                    var spawnerToLog = updatedSpawnerComponentsForLogging[i];
                    // The inner #if for this log is already applied from the previous step
                    DebugLogManager.Instance.Log(
                        $"SpawnerSystem.OnUpdate: Spawner {i}: Count={spawnerToLog.Count}/{spawnerToLog.MaxCount}, Batch={spawnerToLog.Batch}, Interval={spawnerToLog.Interval}, Elapsed={spawnerToLog.Elapsed}",
                        DebugLogSettings.LogType.EnemySpawner);
                }

                updatedSpawnerComponentsForLogging.Dispose();
#endif

                var elapsedTime = (float)state.WorldUnmanaged.Time.ElapsedTime;
                new SpawnerJob
                {
                    //GroupLookup = m_GroupLookup,
                    Ecb = m_CommandBuffer.CreateCommandBuffer(state.WorldUnmanaged),
                    DeltaTime = state.WorldUnmanaged.Time.DeltaTime,
                    ElapsedTime = elapsedTime,
                }.Schedule();
            }
        }

        private void ResetNavigationForPooledEntities(ref SystemState state, EntityCommandBuffer ecb)
        {
            // Reset navigation for entities that were just spawned from pool
            foreach (var (spawnerLink, agentBody, entity) in SystemAPI.Query<RefRO<SpawnerLink>, RefRW<AgentBody>>()
                         .WithEntityAccess()
                         .WithAll<InUse>()
                         .WithNone<NavigationResetTag>()) // Only process entities that haven't been reset yet
            {
                // Get the spawner to set destination
                if (SystemAPI.HasComponent<Spawner>(spawnerLink.ValueRO.SpawnerEntity))
                {
                    var spawner = SystemAPI.GetComponent<Spawner>(spawnerLink.ValueRO.SpawnerEntity);

                    // Reset agent body
                    var newAgentBody = agentBody.ValueRO;
                    newAgentBody.Destination = spawner.Destination;
                    newAgentBody.IsStopped = false;
                    ecb.SetComponent(entity, newAgentBody);

                    // Reset navigation components by toggling them
                    if (SystemAPI.HasComponent<AgentCollider>(entity))
                    {
                        ecb.SetComponentEnabled<AgentCollider>(entity, false);
                        ecb.SetComponentEnabled<AgentCollider>(entity, true);
                    }

                    if (SystemAPI.HasComponent<AgentReciprocalAvoid>(entity))
                    {
                        ecb.SetComponentEnabled<AgentReciprocalAvoid>(entity, false);
                        ecb.SetComponentEnabled<AgentReciprocalAvoid>(entity, true);
                    }

                    if (SystemAPI.HasComponent<AgentSeparation>(entity))
                    {
                        ecb.SetComponentEnabled<AgentSeparation>(entity, false);
                        ecb.SetComponentEnabled<AgentSeparation>(entity, true);
                    }

                    if (SystemAPI.HasComponent<AgentSonarAvoid>(entity))
                    {
                        ecb.SetComponentEnabled<AgentSonarAvoid>(entity, false);
                        ecb.SetComponentEnabled<AgentSonarAvoid>(entity, true);
                    }

                    // Set crowd group if spawner has one
                    // if (spawner.Group != Entity.Null && SystemAPI.HasComponent<AgentCrowdPath>(entity))
                    // {
                    //     ecb.SetSharedComponent(entity, new AgentCrowdPath { Group = spawner.Group });
                    // }

                    // Mark as reset to avoid processing again
                    ecb.AddComponent<NavigationResetTag>(entity);

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    DebugLogManager.Instance.Log($"Reset navigation for pooled entity {entity}",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                }
            }

            // Clean up reset tags after one frame
            // foreach (var entity in SystemAPI.Query<Entity>().WithAll<NavigationResetTag>())
            // {
            //     ecb.RemoveComponent<NavigationResetTag>(entity);
            // }
        }

        private void ResetNavigationComponent<T>(ref SystemState state, EntityCommandBuffer ecb, Entity entity)
            where T : unmanaged, IComponentData, IEnableableComponent
        {
            if (state.EntityManager.HasComponent<T>(entity))
            {
                // Disable then enable to force reset
                ecb.SetComponentEnabled<T>(entity, false);
                ecb.SetComponentEnabled<T>(entity, true);
            }
        }

        private void ProcessSpawner(ref Spawner spawner, float3 position, Entity spawnerEntity,
            EntityCommandBuffer ecb, float deltaTime, float elapsedTime, Entity poolManagerEntity)
        {
            if (spawner.MaxCount == spawner.Count && !spawner.ContinuousSpawning)
            {
                return;
            }

            spawner.Elapsed += deltaTime;

            if (spawner.Elapsed >= spawner.Interval && spawner.Count < spawner.MaxCount)
            {
                spawner.Elapsed -= spawner.Interval;

                int spawnCount = math.min(spawner.Batch, spawner.MaxCount - spawner.Count);

                for (int i = 0; i < spawnCount; i++)
                {
                    float3 offset = spawner.Random.NextFloat3(-spawner.Size, spawner.Size);
                    float3 spawnPosition = position + offset;

                    // Use PoolingUtility to ensure consistent hash calculation
                    uint typeHash = PoolingUtility.GetEnumHash(spawner.EntityType);

#if UNITY_EDITOR
                    UnityEngine.Debug.Log(
                        $"SpawnerSystem: Requesting spawn with typeHash {typeHash} for {spawner.EntityType}");
#endif

                    ecb.AppendToBuffer(poolManagerEntity, new PoolSpawnRequest
                    {
                        TypeHash = typeHash,
                        Position = spawnPosition,
                        Rotation = quaternion.identity,
                        Scale = spawner.PrefabSize,
                        RequestingEntity = spawnerEntity
                    });

                    spawner.Count++;
                }
            }
        }

        // Calculate hash for enum type and value (matches PoolDatabase calculation)
        private static uint CalculateEnumHash<TEnum>(TEnum enumValue) where TEnum : struct, System.Enum
        {
            var typeName = typeof(TEnum).FullName;
            var valueName = enumValue.ToString();
            var typeHash = typeName.GetHashCode();
            var valueHash = valueName.GetHashCode();
            var result = (uint)(typeHash ^ valueHash);

#if UNITY_EDITOR
            UnityEngine.Debug.Log(
                $"CalculateEnumHash: Type={typeName}, Value={valueName}, TypeHash={typeHash}, ValueHash={valueHash}, Result={result}");
#endif

            return result;
        }


        // Remove BurstCompile to allow DebugLogManager.Instance.Log
        partial struct SpawnerJob : IJobEntity
        {
            //[ReadOnly]
            //public ComponentLookup<CrowdGroup> GroupLookup;
            public EntityCommandBuffer Ecb;
            public float DeltaTime;
            public float ElapsedTime;

            public void Execute(Entity spawnerEntity, ref NormalSpawner spawner, in LocalToWorld transform)
            {
                // Debug: Log spawner details in job
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                DebugLogManager.Instance.Log(
                    $"SpawnerJob.Execute: Processing spawner with Count={spawner.Count}/{spawner.MaxCount}, Batch={spawner.Batch}, Interval={spawner.Interval}, Elapsed={spawner.Elapsed}, ContinuousSpawning={spawner.ContinuousSpawning}",
                    DebugLogSettings.LogType.EnemySpawner);
#endif

                // If we've reached max count and continuous spawning is not enabled, skip spawning
                if (spawner.MaxCount == spawner.Count && !spawner.ContinuousSpawning)
                {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    DebugLogManager.Instance.Log(
                        "SpawnerJob.Execute: Spawner has reached max count and continuous spawning is disabled, skipping",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                    return;
                }

                // If we've reached max count and continuous spawning is enabled, we'll still update elapsed time
                // so we're ready to spawn as soon as some entities are killed

                //if (!GroupLookup.TryGetComponent(spawner.Group, out CrowdGroup group))
                //    return;

                spawner.Elapsed += DeltaTime;

                // Only spawn if we have room for more entities or continuous spawning is enabled
                if (spawner.Elapsed >= spawner.Interval && spawner.Count < spawner.MaxCount)
                {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    DebugLogManager.Instance.Log(
                        $"SpawnerJob.Execute: Elapsed time {spawner.Elapsed} >= Interval {spawner.Interval}, spawning batch",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                    spawner.Elapsed -= spawner.Interval;

                    int spawnCount = math.min(spawner.Batch, spawner.MaxCount - spawner.Count);
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    DebugLogManager.Instance.Log(
                        $"SpawnerJob.Execute: Will spawn {spawnCount} entities (Batch={spawner.Batch}, MaxCount={spawner.MaxCount}, Count={spawner.Count}, ContinuousSpawning={spawner.ContinuousSpawning})",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                    for (int i = 0; i < spawnCount; i++)
                    {
                        float3 offset = spawner.Random.NextFloat3(-spawner.Size, spawner.Size);
                        float3 position = transform.Position + offset;
                        Entity unit = Ecb.Instantiate(spawner.Prefab);
                        Ecb.SetComponent(unit,
                            new LocalTransform
                                { Position = position, Scale = spawner.PrefabSize, Rotation = quaternion.identity });
                        Ecb.SetComponent(unit, new AgentBody { Destination = spawner.Destination, IsStopped = false });
                        Ecb.SetSharedComponent(unit, new AgentCrowdPath { Group = spawner.Group });

                        // Add SpawnerLink component to track which spawner created this entity
                        Ecb.AddComponent(unit, new SpawnerLink
                        {
                            SpawnerEntity = spawnerEntity,
                            SpawnerName = spawner.Name // You can set a more descriptive name if needed
                        });

                        // Add SpawnComponent for detection system grace period
                        Ecb.AddComponent(unit, new SpawnComponent
                        {
                            SpawnTime = ElapsedTime
                        });

                        spawner.Count++;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                        DebugLogManager.Instance.Log(
                            $"SpawnerJob.Execute: Spawned entity {i + 1}/{spawnCount}, new Count={spawner.Count}",
                            DebugLogSettings.LogType.EnemySpawner);
#endif
                    }
                }
                else if (spawner.Elapsed >= spawner.Interval && spawner.Count >= spawner.MaxCount &&
                         spawner.ContinuousSpawning)
                {
                    // If continuous spawning is enabled but we're at max capacity, we'll keep the timer ready
                    // for immediate spawning once some entities are killed
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    DebugLogManager.Instance.Log(
                        $"SpawnerJob.Execute: Continuous spawning enabled but at max capacity ({spawner.Count}/{spawner.MaxCount}). Ready to spawn when entities are killed.",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                }
                else
                {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
                    DebugLogManager.Instance.Log(
                        $"SpawnerJob.Execute: Elapsed time {spawner.Elapsed} < Interval {spawner.Interval}, waiting",
                        DebugLogSettings.LogType.EnemySpawner);
#endif
                }
            }
        }
    }

    // Helper tag component for tracking navigation reset
    public struct NavigationResetTag : IComponentData
    {
    }
}